import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/ui/molecules/app_search_bar.dart';
import 'package:melodyze/core/ui/molecules/butons/app_button.dart';
import 'package:melodyze/core/ui/molecules/navigation/melo_scaffold.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/wrappers/app_toast.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/secure_storage_helper.dart';
import 'package:melodyze/modules/preference/bloc/prefence_event.dart';
import 'package:melodyze/modules/preference/bloc/preference_bloc.dart';
import 'package:melodyze/modules/preference/bloc/preference_selection_cubit.dart';
import 'package:melodyze/modules/preference/bloc/preference_state.dart';
import 'package:melodyze/modules/preference/model/preference_list.dart';
import 'package:melodyze/modules/preference/repo/preference_repo.dart';
import 'package:melodyze/modules/preference/service/preference_service.dart';
import 'package:melodyze/modules/preference/widget/preference_grid.dart';

@RoutePage()
class PreferenceScreen extends StatelessWidget {
  const PreferenceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<PreferenceBloc>(
          create: (context) => PreferenceBloc(
            preferenceRepo: PreferenceRepo(
              preferenceService: PreferenceService(),
            ),
            secureStorageHelper: DI().resolve<SecureStorageHelper>(),
          ),
        ),
        BlocProvider<PreferenceSelectionCubit>(
          create: (context) => PreferenceSelectionCubit(),
        ),
      ],
      child: const PreferencePage(),
    );
  }
}

class PreferencePage extends StatefulWidget {
  const PreferencePage({super.key});

  @override
  PreferencePageState createState() => PreferencePageState();
}

class PreferencePageState extends State<PreferencePage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    // Fetch more when user scrolls 75% down
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.30) {
      context.read<PreferenceBloc>().add(PreferenceLoadMoreEvent());
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final selectedPreferences = context.watch<PreferenceSelectionCubit>().getSelectedPreferences();

    return MeloScaffold(
        showBackButton: false,
        body: SafeArea(
          child: Container(
            padding: const EdgeInsets.only(top: 24),
            height: double.infinity,
            width: double.infinity,
            child: Stack(
              children: [
                Column(
                  children: [
                    // Header & Search
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 28.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                'Pick your favorite artists \n& Let the vibes unfold ✨',
                                style: AppTextStyles.text22semiBold,
                              ),
                              Spacer(),
                              Text(
                                '(${selectedPreferences.length}/6)',
                                style: AppTextStyles.text20regular.copyWith(fontFamily: AppFonts.iceland),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          AppSearchBar(
                            searchBarHintText: "Search your favourite artists",
                            onTextChanged: (query) {
                              context.read<PreferenceBloc>().add(PreferenceLoadDataEvent(searchQuery: query));
                            },
                          ),
                          const SizedBox(height: 6),
                        ],
                      ),
                    ),

                    // Scrollable List with Pagination
                    Expanded(
                      child: Stack(
                        children: [
                          SingleChildScrollView(
                            controller: _scrollController, // Attach Scroll Controller
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 28.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(height: 12),
                                  BlocBuilder<PreferenceBloc, BlocState>(
                                    buildWhen: (previous, current) => current is BlocSuccessState || current is LoadingState || current is BlocFailureState,
                                    builder: (context, state) {
                                      if (state is BlocSuccessState<PreferenceList>) {
                                        if (state.data.data.isEmpty) {
                                          return Center(child: Text('No artists found.'));
                                        }
                                        context.read<PreferenceSelectionCubit>().setAllPreferences(state.data.data);

                                        return Column(
                                          children: [
                                            PreferenceGrid(key: ValueKey('success-grid')),
                                            if (context.read<PreferenceBloc>().hasMore) const PreferenceGridShimmer(key: ValueKey('loading-more-shimmer'))
                                          ],
                                        );
                                      } else if (state is LoadingState) {
                                        return const PreferenceGridShimmer(key: ValueKey('loading-shimmer'));
                                      } else if (state is BlocFailureState) {
                                        DI().resolve<AppToast>().showToast(state.error.message);
                                        return const SizedBox.shrink(key: ValueKey('empty'));
                                      } else {
                                        return const SizedBox.shrink(key: ValueKey('empty'));
                                      }
                                    },
                                  ),
                                  const SizedBox(height: 14),
                                ],
                              ),
                            ),
                          ),

                          // Top Gradient Overlay
                          Positioned(
                            top: 0,
                            left: 0,
                            right: 0,
                            child: Container(
                              height: MediaQuery.of(context).size.height * 0.02,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    AppColors.black010101,
                                    AppColors.black010101.withAlpha(0),
                                  ],
                                ),
                              ),
                            ),
                          ),

                          // Bottom Gradient Overlay
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: IgnorePointer(
                              child: Container(
                                height: 12, // Adjust the height as needed
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.bottomCenter,
                                    end: Alignment.topCenter,
                                    colors: [
                                      AppColors.darkCyan, // Fully transparent at the top
                                      AppColors.darkCyan.withAlpha(0), // Fully transparent at the top
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    )
                  ],
                ),

                // Bottom Button
                Positioned(
                  bottom: 12,
                  left: 0,
                  right: 0,
                  child: BlocConsumer<PreferenceBloc, BlocState>(
                    listenWhen: (previous, current) => current is SaveBlocSuccessState,
                    listener: (context, state) {
                      if (state is SaveBlocSuccessState) {
                        context.replaceRoute(DashboardRoute());
                      }
                    },
                    buildWhen: (previous, current) => current is SaveBlocSuccessState || current is SaveLoadingState || current is SaveBlocFailureState,
                    builder: (context, state) {
                      if (context.watch<PreferenceSelectionCubit>().isSelectionValid()) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24.0),
                          child: AnimatedOpacity(
                            duration: const Duration(milliseconds: 200),
                            opacity: 1,
                            child: AppButton(
                              text: 'Proceed',
                              gradient: AppGradients.gradientPurpleTealTransparent,
                              borderGradient: AppGradients.gradientPurpleBorder,
                              backgroundColor: Colors.transparent,
                              textFontSize: 16,
                              isLoading: state is SaveLoadingState,
                              onPressed: () {
                                context.read<PreferenceBloc>().add(SavePreferenceEvent(selectedPreferences));
                              },
                            ),
                          ),
                        );
                      }
                      return SizedBox.shrink();
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
    );
  }
}
