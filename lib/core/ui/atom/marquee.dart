import 'package:flutter/material.dart';
import 'package:marquee/marquee.dart';

class Marque<PERSON> extends StatelessWidget {
  final String text;
  final TextStyle style;
  final double width;
  final double blankSpace;
  final double velocity;
  final Duration pauseAfterRound;
  final Duration startAfter;
  final bool showFadingOnlyWhenScrolling;
  final double fadingEdgeStartFraction;
  final double fadingEdgeEndFraction;
  final CrossAxisAlignment crossAxisAlignment;

  const Marqueee({
    super.key,
    required this.text,
    required this.style,
    required this.width,
    this.blankSpace = 20.0,
    this.velocity = 30.0,
    this.pauseAfterRound = const Duration(seconds: 1),
    this.startAfter = const Duration(seconds: 3),
    this.showFadingOnlyWhenScrolling = true,
    this.fadingEdgeStartFraction = 0.0,
    this.fadingEdgeEndFraction = 0.1,
    this.crossAxisAlignment = CrossAxisAlignment.start,
  });

  @override
  Widget build(BuildContext context) {
    final textSpan = TextSpan(
      text: text,
      style: style,
    );
    final textPainter = TextPainter(
      text: textSpan,
      maxLines: 1,
      textScaler: TextScaler.noScaling,
      textDirection: TextDirection.ltr,
    )..layout(maxWidth: double.infinity);

    return SizedBox(
      width: width,
      height: textPainter.height + 5,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final needsMarquee = (textPainter.width + blankSpace) > constraints.maxWidth;

          if (needsMarquee) {
            return Marquee(
              text: text,
              style: style,
              scrollAxis: Axis.horizontal,
              crossAxisAlignment: crossAxisAlignment,
              blankSpace: blankSpace,
              velocity: velocity,
              pauseAfterRound: pauseAfterRound,
              showFadingOnlyWhenScrolling: showFadingOnlyWhenScrolling,
              fadingEdgeStartFraction: fadingEdgeStartFraction,
              fadingEdgeEndFraction: fadingEdgeEndFraction,
              startAfter: startAfter,
            );
          } else {
            return Text(
              text,
              style: style,
              maxLines: 1,
              overflow: TextOverflow.visible,
            );
          }
        },
      ),
    );
  }
}
